---
description:
globs: apps/graphql-api/src/**
alwaysApply: false
---
# GraphQL API

Use `Valid` decorator for validating DTO objects. Do not add any other 3rd party validation libraries.

When validating DTO objects use the following rules:

```
  @Valid(Valid.scheme.string().optional(), { isOptional: true })
```
When passing a value that's optional, make sure to include the `{ isOptional: true }` argument as well as call `.optional()` on the scheme.

For GraphQL susbcription functionality use package `graphql-yoga`

## Directory structure

```
./apps/graphql-api
├── environment.d.ts
├── package.json
├── src
│   ├── index.ts
│   ├── modules
│   │   ├── acl
│   │   │   ├── core
│   │   │   │   └── acl-user-role.value-object.ts
│   │   │   ├── dtos
│   │   │   │   └── roles-enum.ts
│   │   │   └── interface
│   │   │       ├── auth-roles.ts
│   │   │       ├── check-roles-exist.test.ts
│   │   │       ├── check-roles-exist.ts
│   │   │       ├── has-access.ts
│   │   │       ├── has-root.ts
│   │   │       └── validate-role.ts
│   │   ├── app
│   │   │   ├── core
│   │   │   │   └── open-link-by-domain.value-object.ts
│   │   │   ├── dtos
│   │   │   │   ├── default-error.dto.ts
│   │   │   │   ├── error-fabric.dto.ts
│   │   │   │   └── status-ok.dto.ts
│   │   │   ├── index.ts
│   │   │   └── interface
│   │   │       ├── generate-open-href.ts
│   │   │       └── logger.ts
│   │   ├── auth
│   │   │   ├── core
│   │   │   │   ├── mappers
│   │   │   │   └── stored-token-data.value-object.ts
│   │   │   ├── dtos
│   │   │   │   ├── auth-user.dto.ts
│   │   │   │   ├── forgot-password-errors.dto.ts
│   │   │   │   ├── forgot-password-result.union.ts
│   │   │   │   ├── login-errors.dto.ts
│   │   │   │   ├── login-result.union.ts
│   │   │   │   ├── refresh-credentials-result.union.ts
│   │   │   │   ├── refresh-credentials.dto.ts
│   │   │   │   ├── refresh-errors.dto.ts
│   │   │   │   └── tokens.dto.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── forgot-password-input.dto.ts
│   │   │   │   ├── magic-login-input.dto.ts
│   │   │   │   ├── refresh-credentials-input.dto.ts
│   │   │   │   └── user-login-input.dto.ts
│   │   │   ├── interface
│   │   │   │   ├── generate-forgot-href.ts
│   │   │   │   └── tokens
│   │   │   └── resolvers
│   │   │       ├── forgot-password
│   │   │       ├── magic-login.mutation.resolver.ts
│   │   │       ├── refresh-credentials.mutations.resolver.ts
│   │   │       └── user-login
│   │   ├── company
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── company-list-result-union.ts
│   │   │   │   ├── company-list.dto.ts
│   │   │   │   ├── company-user.dto.ts
│   │   │   │   ├── company.dto.ts
│   │   │   │   └── switch-company-result.union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   └── switch-company-input.dto.ts
│   │   │   └── resolvers
│   │   │       └── company.resolver.ts
│   │   ├── company-manager
│   │   │   ├── dtos
│   │   │   │   ├── add-company-by-root-result.union.ts
│   │   │   │   ├── company-suggestions-result-union.ts
│   │   │   │   ├── edit-company-by-root-result.union.ts
│   │   │   │   ├── remove-company-by-root-result.union.ts
│   │   │   │   └── root-company-list-result-union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── add-company-by-root-input.dto.ts
│   │   │   │   ├── edit-company-by-root-input.dto.ts
│   │   │   │   ├── get-company-suggestions-args.dto.ts
│   │   │   │   ├── get-company-notification-list-filters.input.dto.ts
│   │   │   │   └── remove-company-by-root-input.dto.ts
│   │   │   └── resolvers
│   │   │       └── root-company.resolver.ts
│   │   ├── contact
│   │   │   ├── contact.mutations.resolver.ts
│   │   │   ├── contact.queries.resolver.ts
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── address.dto.ts
│   │   │   │   ├── contact-list.dto.ts
│   │   │   │   ├── contact-list.union.ts
│   │   │   │   ├── contact-order.dto.ts
│   │   │   │   ├── contact-type.enum.ts
│   │   │   │   ├── contact.dto.ts
│   │   │   │   ├── contact.union.ts
│   │   │   │   ├── note-list.dto.ts
│   │   │   │   ├── note-list.union.ts
│   │   │   │   ├── note.dto.ts
│   │   │   │   ├── note.union.ts
│   │   │   │   └── phone.dto.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── address.input.ts
│   │   │   │   ├── contact-delete.input.ts
│   │   │   │   ├── contact-list-filters.dto.ts
│   │   │   │   ├── contact-upsert.input.ts
│   │   │   │   ├── get-contact-args.dto.ts
│   │   │   │   ├── get-contacts-list-args.dto.ts
│   │   │   │   ├── get-notes-list-args.dto.ts
│   │   │   │   ├── note-delete.input.ts
│   │   │   │   ├── note-upsert.input.ts
│   │   │   │   └── phone.input.ts
│   │   │   └── value-objects
│   │   │       └── contact-list-sort.value-object.ts
│   │   ├── index.ts
│   │   ├── mail-sender
│   │   │   └── interface
│   │   │       └── send-email.ts
│   │   ├── notification
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── notification-list.dto.ts
│   │   │   │   ├── notification-list.union.ts
│   │   │   │   ├── notification-settings-type.enum.ts
│   │   │   │   ├── notification-settings.union.ts
│   │   │   │   ├── notification.dto.ts
│   │   │   │   ├── notifications-count.dto.ts
│   │   │   │   └── notifications-count.union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── get-notification-list-args.dto.ts
│   │   │   │   └── notification-read.input.ts
│   │   │   ├── notification.mutations.resolver.ts
│   │   │   └── notification.queries.resolver.ts
│   │   ├── order
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── item.dto.ts
│   │   │   │   ├── order-list.dto.ts
│   │   │   │   ├── order-list.union.ts
│   │   │   │   ├── order-status.enum.ts
│   │   │   │   ├── order.dto.ts
│   │   │   │   ├── order.union.ts
│   │   │   │   └── sku.dto.ts
│   │   │   ├── index.ts
│   │   │   ├── input
│   │   │   │   ├── get-order-args.dto.ts
│   │   │   │   ├── get-order-list-by-contact-args.dto.ts
│   │   │   │   └── get-order-list-by-name-args.dto.ts
│   │   │   ├── interface
│   │   │   │   └── handle-order-list-result.ts
│   │   │   └── order.resolver.ts
│   │   ├── otp
│   │   │   ├── dtos
│   │   │   │   ├── otp-disable.union.ts
│   │   │   │   ├── otp-enable.union.ts
│   │   │   │   ├── otp-errors.ts
│   │   │   │   ├── otp-generate.dto.ts
│   │   │   │   ├── otp-generate.union.ts
│   │   │   │   ├── otp-verify-action-result.union.ts
│   │   │   │   └── otp-verify-login-result.union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── otp-code-input.dto.ts
│   │   │   │   └── otp-verify-action-input.dto.ts
│   │   │   ├── inteface
│   │   │   │   ├── get-otp-auth.ts
│   │   │   │   ├── otp-generate.ts
│   │   │   │   ├── otp-required-answer.ts
│   │   │   │   ├── otp-verify-code-by-user-uuid.ts
│   │   │   │   ├── otp-verify-code.ts
│   │   │   │   └── verify-input.ts
│   │   │   └── resolvers
│   │   │       ├── otp-disable.resolver.ts
│   │   │       ├── otp-enable.resolver.ts
│   │   │       ├── otp-generate.resolver.ts
│   │   │       └── otp-verify-action.resolver.ts
│   │   ├── pagination
│   │   │   ├── core
│   │   │   │   └── pagination-cursor.mapper.ts
│   │   │   └── dtos
│   │   │       ├── pagination-cursor-args.dto.ts
│   │   │       └── pagination-cursor.dto.ts
│   │   ├── resolvers.ts
│   │   ├── server
│   │   │   ├── core
│   │   │   │   └── gql-server-context.ts
│   │   │   └── interface
│   │   │       ├── create-context.ts
│   │   │       └── get-token-from-headers.ts
│   │   ├── template
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── template-list.dto.ts
│   │   │   │   ├── template-list.union.ts
│   │   │   │   ├── template.dto.ts
│   │   │   │   └── template.union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── get-template-args.dto.ts
│   │   │   │   ├── get-templates-list-args.dto.ts
│   │   │   │   ├── template-delete.input.ts
│   │   │   │   └── template-upsert.input.ts
│   │   │   ├── template.mutations.resolver.ts
│   │   │   └── template.queries.resolver.ts
│   │   ├── token
│   │   │   ├── core
│   │   │   │   ├── invite-token-data.value-object.ts
│   │   │   │   ├── magic-token-data.value-object.ts
│   │   │   │   └── opt-token-data.value-object.ts
│   │   │   └── interface
│   │   │       ├── access-refresh-tokens
│   │   │       ├── invite
│   │   │       ├── magic
│   │   │       ├── otp
│   │   │       ├── token-algo.ts
│   │   │       └── validate-token.ts
│   │   ├── user
│   │   │   ├── core
│   │   │   │   └── mappers
│   │   │   ├── dtos
│   │   │   │   ├── change-password.union.ts
│   │   │   │   ├── get-user.error.ts
│   │   │   │   ├── send-feedback.union.ts
│   │   │   │   ├── update-user.union.ts
│   │   │   │   ├── user.dto.ts
│   │   │   │   └── user.union.ts
│   │   │   ├── index.ts
│   │   │   ├── inputs
│   │   │   │   ├── change-password-input.dto.ts
│   │   │   │   ├── send-feedback-input.dto.ts
│   │   │   │   └── update-user-input.dto.ts
│   │   │   ├── user-manage.resolver.ts
│   │   │   └── user.queries.resolver.ts
│   │   ├── user-manager
│   │   │   ├── core
│   │   │   │   ├── account-type-enum.ts
│   │   │   │   ├── change-status-company-user-enum.ts
│   │   │   │   └── company-user-status-enum.ts
│   │   │   ├── dtos
│   │   │   │   ├── add-user-error.dto.ts
│   │   │   │   ├── add-user-result-union.ts
│   │   │   │   ├── apply-invite-result.union.ts
│   │   │   │   ├── change-status-company-user-result.union.ts
│   │   │   │   ├── edit-company-user-result.union.ts
│   │   │   │   ├── user-details-list.dto.ts
│   │   │   │   ├── user-details.dto.ts
│   │   │   │   └── user-list-result-union.ts
│   │   │   ├── index.ts
│   │   │   ├── input
│   │   │   │   ├── add-user-input.dto.ts
│   │   │   │   ├── apply-invite-input.dto.ts
│   │   │   │   ├── change-status-company-user-input.dto.ts
│   │   │   │   ├── edit-company-user-input.dto.ts
│   │   │   │   ├── get-user-list-args.dto.ts
│   │   │   │   └── user-list-filters.input.dto.ts
│   │   │   ├── interface
│   │   │   │   ├── generate-invite-href.ts
│   │   │   │   ├── get-company-user-status.ts
│   │   │   │   ├── handle-add-user.ts
│   │   │   │   ├── handle-change-user.ts
│   │   │   │   ├── handle-revoke-user.ts
│   │   │   │   └── validate-invite.ts
│   │   │   └── resolvers
│   │   │       ├── invite.resolver.ts
│   │   │       ├── render-invite-template.html.ts
│   │   │       └── user-management.resolver.ts
│   │   └── validator
│   │       ├── dtos
│   │       │   ├── validation-error.dto.ts
│   │       │   └── validation-item.dto.ts
│   │       ├── index.ts
│   │       └── interface
│   │           ├── fields
│   │           ├── valid.ts
│   │           ├── validate
│   │           └── validate-args
│   └── server
│       ├── express.server.ts
│       ├── gql-apollo.server.ts
│       ├── gql-yoga.server.ts
│       └── middlewares
│           ├── decorate-lcid-logger.middleware.ts
│           ├── error-interceptor.middleware.ts
│           └── gql-auth-checker.middleware.ts
├── tsconfig.json
└── tsup.config.ts

92 directories, 202 files

```


## GraphQL Application Architecture

## Role of Each Major Folder

- **Modules**: Encapsulate domain-specific logic, adhering to domain-driven design principles.
- **Server**: Manages server configuration and middleware, integrating Express and Yoga Server. (Apollo used only for playground)
- **Prisma**: Manages database schema and migrations, providing a central place for database-related configurations.

### Domain-Driven Design Principles

- **Use Cases**: Encapsulate business logic, providing a clear API for application operations.
- **Adapters**: Abstract database interactions, allowing for separation of concerns and easier testing.
- **Value Objects**: Used to represent complex data structures, ensuring consistency and validation across the application.

This structured approach ensures maintainability and scalability, allowing for clear separation of concerns and adherence to best practices in software design.

## Code Examples

### Get Company List Use Case

```
import { resultErr, resultOk } from '@repo/result'

import { GetCompanyListAdapter } from '~/modules/company/infra/get-company-list.adapter'
import { GetCompanyListByUserAdapter } from '~/modules/company/infra/get-company-list-by-user.adapter'
import { prisma } from '~/prisma.connection'

import { companyErrors } from '../core'

import type { Result } from '@repo/result'
import type { CompanyListRelationsValueObject } from '~/modules/company/core/value-objects/company-list-relations.value-object'
import type { DBPaginationCursorValueObject } from '~/modules/pagination/core/value-objects/db-pagination-cursor-value.object'
import type { CompanyErrorsValueObject } from '../core'

export class GetCompanyListUseCase {
  constructor(
    private readonly adapter = new GetCompanyListAdapter(prisma),
    private readonly getByUserAdapter = new GetCompanyListByUserAdapter(prisma),
  ) {}

  async getCompanyList({
    pagination,
    name,
  }: {
    pagination?: DBPaginationCursorValueObject
    name?: string
  }): Promise<
    Result<
      {
        companyList: CompanyListRelationsValueObject[]
        pagination: DBPaginationCursorValueObject
      },
      CompanyErrorsValueObject
    >
  > {
    try {
      const result = await this.adapter.getList({
        pagination,
        name,
      })
      if (!result) return resultErr(companyErrors('GetCompaniesNotExist'))

      return resultOk({
        pagination: result.pagination,
        companyList: result.list.map((company) => ({
          company,
        })),
      })
    } catch (e: unknown) {
      const error = companyErrors('GetCompaniesUnexpected', e)
      return resultErr(error)
    }
  }

  async getCompanyListByUser({
    userUuid,
  }: {
    userUuid: string
  }): Promise<
    Result<
      { companyList: CompanyListRelationsValueObject[] },
      CompanyErrorsValueObject
    >
  > {
    try {
      const result = await this.getByUserAdapter.getList({
        userUuid,
      })
      if (!result) return resultErr(companyErrors('GetCompaniesNotExist'))

      return resultOk({
        companyList: result.map((company) => ({
          company,
        })),
      })
    } catch (e: unknown) {
      const error = companyErrors('GetCompaniesUnexpected', e)
      return resultErr(error)
    }
  }
}

```
