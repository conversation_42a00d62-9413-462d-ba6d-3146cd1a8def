import { isErr } from '@repo/result'
import { initUpdate } from './init-update'
import { CustomerCreateWebhookPayload } from './types/customer-create-webhook.type'
import { UpsertShopifyCustomerUseCase } from '@emma/storage'
import { generateInviteToken } from './generate-invite-token'
import { generateOpenHref } from './generate-invite-href'
import { renderInviteTemplateHtml } from './render-invite-template'
import { sendEmail } from '@repo/sender'

export class ShopifyCustomer {
  constructor() {}

  async upsertShopifyCustomer({
    payload,
    shop,
  }: {
    payload: CustomerCreateWebhookPayload
    shop: string
  }) {
    const shopifyId = payload.id.toString()

    const { shopifyCompany, isPartner } = await initUpdate({
      shopifyId,
      shop,
    })

    if (!shopifyCompany) {
      console.error('Could not init update due to missing shopify company')
      return
    }

    const upsertShopifyCustomerUseCase = new UpsertShopifyCustomerUseCase()

    const { token, expiredAt } = generateInviteToken({
      email: payload.email,
      company: {
        uuid: shopifyCompany.company.public_uuid,
        name: shopifyCompany.company.name,
        createdAt: shopifyCompany.company.created_at,
        lang: shopifyCompany.company.lang,
      },
      firstName: payload.first_name ?? '',
      lastName: payload.last_name ?? '',
      roles: ['member'],
    })

    const upsertShopifyCustomerResult =
      await upsertShopifyCustomerUseCase.upsertShopifyCustomer({
        shopifyId,
        shopifyCompanyId: shopifyCompany.shopify_company_id,
        email: payload.email,
        firstName: payload.first_name ?? '',
        lastName: payload.last_name ?? '',
        isPartner,
        invite: {
          inviteStatus: 'send',
          token,
          expiredAt,
        },
      })

    if (isErr(upsertShopifyCustomerResult)) {
      console.error(
        'Error upserting Shopify customer:',
        upsertShopifyCustomerResult.error,
      )
      return
    }

    console.log('Shopify customer upserted:', shopifyId)
    if (!isPartner) {
      return
    }
    const { href } = generateOpenHref({
      passData: token,
      domain: 'web',
      route: 'sign-in/invite',
    })

    if (!href) {
      console.error('Error generating invite href')
      return
    }

    const sendRes = await sendEmail({
      subject: `EMMA: Invite to "${shopifyCompany.company.name}"`,
      to: payload.email,
      html: renderInviteTemplateHtml({
        href,
      }),
    })

    if (isErr(sendRes)) {
      console.error('Error sending email:', sendRes.error)
      return
    }

    console.log('Email sent:', payload.email)
  }
}
