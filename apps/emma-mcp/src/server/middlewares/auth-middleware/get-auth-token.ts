import { isToolInvocationRequest } from './is-tool-invocation-request'

import type { Request } from 'express'
import type { IncomingHttpHeaders } from 'node:http'

function getBearerToken(headers?: IncomingHttpHeaders): string {
  if (!headers) return ''
  const authorizationHeader =
    'Authorization' in headers ? 'Authorization' : 'authorization'

  const reqHeader = headers[authorizationHeader]
    ? String(headers[authorizationHeader])
    : undefined

  const parts = (reqHeader || '').split(' ')
  if (parts.length !== 2) return ''
  return parts[1]
}

export function getAuthToken(req: Request): string {
  const bearerToken = getBearerToken(req.headers)
  if (bearerToken) {
    return bearerToken
  }

  if (!isToolInvocationRequest(req)) return ''

  return getBearerToken(req.body?.params?.headers)
}
