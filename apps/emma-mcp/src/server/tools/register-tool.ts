import { createAuthContext } from './create-auth-context'

import type {
  Mcp<PERSON>erver,
  ToolCallback,
} from '@modelcontextprotocol/sdk/server/mcp.js'
import type { CallToolResult } from '@modelcontextprotocol/sdk/types.js'
import type { ValidatorSchemeObject } from '@repo/validator'
import type { AuthContextValueObject } from '~/system/core/auth-context.value-object'
import type { ToolNamesValueObject } from '~/system/core/tool-names.value-object'

type ExtractRawShape<T> =
  T extends ValidatorSchemeObject<infer Shape> ? Shape : never

export function registerTool<
  Shape extends ValidatorSchemeObject<any>,
  <PERSON><PERSON>s extends ExtractRawShape<Shape>,
  TCb extends ToolCallback<Args>,
>(
  server: McpServer,
  options: {
    name: ToolNamesValueObject
    description: string
    schema: Shape
  },
  cb: (
    args: Parameters<TCb>[0],
    authContext: AuthContextValueObject,
  ) => Promise<CallToolResult>,
) {
  return server.tool(
    options.name,
    options.description,
    options.schema.shape,
    async (args, context): Promise<CallToolResult> => {
      const authContext = createAuthContext(context.authInfo?.extra)

      return cb(args, authContext)
    },
  )
}
