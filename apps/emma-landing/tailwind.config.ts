import sharedConfig from '@repo/tailwind-config/tailwind.config'

import type { Config } from 'tailwindcss'

const config: Config = {
  ...sharedConfig,
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/ui/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      xl: { max: '1280px' },
      lg: { max: '992px' },
      md: { max: '768px' },
      mb: { max: '468px' },
      xs: { max: '394px' },
    },
    extend: {
      colors: {
        ...sharedConfig.theme?.extend?.colors,
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      boxShadow: {
        'feature-card': 'box-shadow: 0 1px 16px -10px rgba(0, 0, 0, .14)',
      },
      keyframes: {
        'fade-in': {
          '0%': {
            transform:
              'translate3d(0px, 32px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg)',
            opacity: '0.8',
            'transform-style': 'preserve-3d',
            'will-change': 'transform, opacity',
          },
          '100%': {
            transform:
              'translate3d(0px, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg)',
            opacity: '1',
            'transform-style': 'preserve-3d',
          },
        },
      },
      animation: {
        'fade-in': 'fade-in 0.5s ease-in-out',
      },
    },
  },
  plugins: [],
}
export default config
