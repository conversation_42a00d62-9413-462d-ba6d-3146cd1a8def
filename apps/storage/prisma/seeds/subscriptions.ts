import { faker } from '@faker-js/faker'
import { assertTypes, type TypeEqualityGuard } from '@repo/result'

import type { PrismaClient, Sku } from '@prisma/client'
import type { DBOrderStatusValueObject } from '~/modules/order'
import type { DBSubscriptionStatusValueObject } from '~/modules/subscription'

type TPrisma = InstanceType<typeof PrismaClient>

// TODO: create more effective seeding for subscriptions

// Create a const array from the union type at runtime
const subscriptionStatuses: DBSubscriptionStatusValueObject[] = [
  'Active',
  'Paused',
  'Cancelled',
  'Completed',
  'Skipped',
  'PaymentFailed',
]

const ORDER_STATUSES = {
  Created: 'Created',
  Processed: 'Processed',
  Shipped: 'Shipped',
  Delivered: 'Delivered',
  Canceled: 'Canceled',
} as const

type OrderStatusesKeys = keyof typeof ORDER_STATUSES

assertTypes<TypeEqualityGuard<OrderStatusesKeys, DBOrderStatusValueObject>>()

/**
 * Common subscription periodicity values in days
 * These are example values for seeding purposes only
 */
const SUBSCRIPTION_PERIODICITIES = {
  WEEKLY: 7,
  BIWEEKLY: 14,
  MONTHLY: 30,
  BIMONTHLY: 60,
  QUARTERLY: 90,
}

const PERIODICITY_VALUES = Object.values(SUBSCRIPTION_PERIODICITIES)

type SubscriptionSkuGeneratorArgs = {
  skus: Pick<Sku, 'id'>[]
  subscriptionId: bigint
}

const generateSubscriptionSkus = ({
  skus,
  subscriptionId,
}: SubscriptionSkuGeneratorArgs) => {
  const selectedSkus = faker.helpers.arrayElements(
    skus,
    faker.number.int({ min: 1, max: Math.min(3, skus.length) }),
  )

  return selectedSkus.map((sku) => ({
    sku_id: sku.id,
    subscription_id: subscriptionId,
  }))
}

type SubscriptionGeneratorArgs = {
  contactId: bigint
}

const generateSubscription = ({ contactId }: SubscriptionGeneratorArgs) => {
  const startDate = faker.date.past()
  const status = faker.helpers.arrayElement(subscriptionStatuses)

  const endDate =
    status === 'Cancelled' || status === 'Completed'
      ? faker.date.between({ from: startDate, to: new Date() })
      : null

  const res: Parameters<TPrisma['subscription']['create']>[0]['data'] = {
    periodicity: faker.helpers.arrayElement(PERIODICITY_VALUES),
    start_date: startDate,
    end_date: endDate,
    status,
    contact_id: contactId,
    created_at: faker.date.past(),
  }

  return res
}

type GenerateSubscriptionOrdersArgs = {
  subscription: {
    id: bigint
    start_date: Date
    end_date: Date | null
    periodicity: number
    status: DBSubscriptionStatusValueObject
    contact_id: bigint
  }
  subscriptionSkus: { sku_id: bigint }[]
  contact: {
    id: bigint
    company_user_id: bigint
    contact_addresses: {
      id: bigint
    }[]
  }
}

const generateSubscriptionOrders = async (
  prisma: PrismaClient,
  { subscription, subscriptionSkus, contact }: GenerateSubscriptionOrdersArgs,
) => {
  const now = new Date()
  const startDate = subscription.start_date
  const endDate = subscription.end_date || now

  const address = contact.contact_addresses[0]

  // Calculate total price for subscription SKUs
  const skus = await prisma.sku.findMany({
    where: {
      id: { in: subscriptionSkus.map((s) => s.sku_id) },
    },
    select: {
      id: true,
      current_price_cents: true,
    },
  })

  const totalPrice = skus.reduce((sum, sku) => sum + sku.current_price_cents, 0)
  const shippingCost = faker.number.int({ min: 1000, max: 3000 })

  // Generate past orders for all subscription statuses
  let orderDate = new Date(startDate)
  const effectiveEndDate = subscription.status === 'Active' ? now : endDate

  const orders = []
  while (orderDate < effectiveEndDate) {
    orders.push({
      subscription_id: subscription.id,
      status: ORDER_STATUSES.Shipped,
      upcoming_at: orderDate,
      created_at: faker.date.past({ refDate: orderDate }),
      type: 'online' as const,
      price_currency: 'USD',
      total_cost_cents: totalPrice,
      shipping_cost_cents: shippingCost,
      tracking_link: faker.string.alphanumeric({ length: 8 }),
      shipping_address_id: address.id,
      billing_address_id: address.id,
      contact_id: contact.id,
      company_user_id: contact.company_user_id,
    })

    orderDate = new Date(
      orderDate.getTime() + subscription.periodicity * 24 * 60 * 60 * 1000,
    )
  }

  // Add one future order for all subscriptions except Completed
  if (subscription.status !== 'Completed') {
    const futureOrderStatus =
      subscription.status === 'Cancelled'
        ? ORDER_STATUSES.Canceled
        : ORDER_STATUSES.Created

    orders.push({
      subscription_id: subscription.id,
      status: futureOrderStatus,
      upcoming_at: orderDate,
      created_at: now,
      type: 'online' as const,
      price_currency: 'USD',
      total_cost_cents: totalPrice,
      shipping_cost_cents: shippingCost,
      tracking_link: faker.string.alphanumeric({ length: 8 }),
      shipping_address_id: address.id,
      billing_address_id: address.id,
      contact_id: contact.id,
      company_user_id: contact.company_user_id,
    })
  }

  // Create all orders in a single batch
  await prisma.order.createMany({
    data: orders,
  })

  // Get the created order IDs
  const orderIds = await prisma.order.findMany({
    where: {
      subscription_id: subscription.id,
    },
    select: {
      id: true,
    },
  })

  // Create all order items in a single batch
  const orderItems = orderIds.flatMap((order) =>
    skus.map((sku) => ({
      order_id: order.id,
      sku_id: sku.id,
      price_cents: sku.current_price_cents,
    })),
  )

  await prisma.orderItem.createMany({
    data: orderItems,
  })
}

export const seedSubscriptions = async (prisma: PrismaClient) => {
  const [companyUserList, skus] = await Promise.all([
    prisma.companyUser.findMany({
      include: {
        contacts: true,
      },
    }),
    prisma.sku.findMany({
      select: {
        company_id: true,
        id: true,
      },
    }),
  ])

  // Prepare all subscription data in memory
  const subscriptionsToCreate: Array<{
    periodicity: number
    start_date: Date
    end_date: Date | null
    status: DBSubscriptionStatusValueObject
    contact_id: bigint
    created_at: Date
  }> = []
  const subscriptionSkusToCreate: Array<{
    sku_id: bigint
    subscription_id: bigint
  }> = []
  const subscriptionOrdersToCreate: GenerateSubscriptionOrdersArgs[] = []

  for (const companyUser of companyUserList) {
    for (const contact of companyUser.contacts) {
      // Check if contact has addresses and get all needed data at once
      const contactWithData = await prisma.contact.findUnique({
        where: { id: contact.id },
        include: {
          contact_addresses: true,
          company_user: true,
        },
      })

      if (!contactWithData?.contact_addresses.length) {
        continue
      }

      // Only create subscriptions for some contacts (30% chance)
      if (faker.datatype.boolean(0.3)) {
        const subscriptionsCount = faker.number.int({
          min: 1,
          max: 3,
        })

        for (let i = 0; i < subscriptionsCount; i++) {
          const subscriptionData = generateSubscription({
            contactId: contact.id,
          })

          const subscription = await prisma.subscription.create({
            data: subscriptionData,
          })

          // Create subscription SKUs
          const subscriptionSkus = generateSubscriptionSkus({
            skus: skus.filter(
              (sku) => sku.company_id === companyUser.company_id,
            ),
            subscriptionId: subscription.id,
          })

          if (subscriptionSkus.length > 0) {
            subscriptionsToCreate.push({
              periodicity: subscriptionData.periodicity,
              start_date: new Date(subscriptionData.start_date),
              end_date: subscriptionData.end_date
                ? new Date(subscriptionData.end_date)
                : null,
              status: subscriptionData.status,
              contact_id: contact.id,
              created_at:
                subscriptionData.created_at instanceof Date
                  ? subscriptionData.created_at
                  : new Date(),
            })
            subscriptionSkusToCreate.push(...subscriptionSkus)
            subscriptionOrdersToCreate.push({
              subscription,
              subscriptionSkus,
              contact: contactWithData,
            })
          }
        }
      }
    }
  }

  // Batch create all subscriptions
  await prisma.subscription.createMany({
    data: subscriptionsToCreate,
  })

  // Batch create all subscription SKUs
  await prisma.subscriptionSku.createMany({
    data: subscriptionSkusToCreate,
  })

  // Process orders in parallel batches
  const batchSize = 10
  for (let i = 0; i < subscriptionOrdersToCreate.length; i += batchSize) {
    const batch = subscriptionOrdersToCreate.slice(i, i + batchSize)
    await Promise.all(
      batch.map((data) => generateSubscriptionOrders(prisma, data)),
    )
  }
}
