import type { PrismaClient } from '@prisma/client'
import type { AutoSkuInputValueObject, ExistingSkuValueObject } from '../core'

export class FindOrCreateSkuAdapter {
  constructor(private readonly prisma: PrismaClient) {}

  async findByShopifyProduct(
    shopifyProductId: string,
    shopifyVariantId?: string,
  ): Promise<ExistingSkuValueObject | null> {
    const sku = await this.prisma.sku.findFirst({
      where: {
        shopify_product_id: shopifyProductId,
        shopify_variant_id: shopifyVariantId || null,
      },
    })

    if (!sku) {
      return null
    }

    return {
      id: sku.id,
      publicUuid: sku.public_uuid,
      name: sku.name,
      code: sku.code,
      currentPriceCents: sku.current_price_cents,
      autoCreated: sku.auto_created,
      shopifyProductId: sku.shopify_product_id || undefined,
      shopifyVariantId: sku.shopify_variant_id || undefined,
      imageUrl: sku.image_url || undefined,
    }
  }

  async createAutoSku(
    input: AutoSkuInputValueObject,
  ): Promise<ExistingSkuValueObject> {
    // Generate SKU code for Shopify products
    const variantSuffix = input.shopifyVariantId
      ? `-${input.shopifyVariantId}`
      : ''
    const skuCode = `SHOP-${input.shopifyProductId}${variantSuffix}`

    // Generate SKU name from product and variant titles
    const skuName = input.variantTitle
      ? `${input.productTitle} - ${input.variantTitle}`
      : input.productTitle

    const sku = await this.prisma.sku.create({
      data: {
        name: skuName,
        code: skuCode,
        image_url: input.imageUrl || null,
        current_price_cents: input.priceCents,
        shopify_product_id: input.shopifyProductId,
        shopify_variant_id: input.shopifyVariantId,
        auto_created: true,
        company: {
          connect: {
            id: input.companyId,
          },
        },
      },
    })

    return {
      id: sku.id,
      publicUuid: sku.public_uuid,
      name: sku.name,
      code: sku.code,
      currentPriceCents: sku.current_price_cents,
      autoCreated: sku.auto_created,
      shopifyProductId: sku.shopify_product_id || undefined,
      shopifyVariantId: sku.shopify_variant_id || undefined,
      imageUrl: sku.image_url || undefined,
    }
  }

  async updateSkuPrice(skuId: bigint, priceCents: number): Promise<void> {
    await this.prisma.sku.update({
      where: { id: skuId },
      data: { current_price_cents: priceCents },
    })
  }

  async updateSkuImage(skuId: bigint, imageUrl: string): Promise<void> {
    await this.prisma.sku.update({
      where: { id: skuId },
      data: { image_url: imageUrl },
    })
  }
}
