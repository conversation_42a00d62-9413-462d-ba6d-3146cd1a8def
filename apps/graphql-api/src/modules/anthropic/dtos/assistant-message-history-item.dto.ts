import { Field, ObjectType } from 'type-graphql'

import { AssistantMessageTypeEnum } from '../core/enums/assistant-message-type.enum'

@ObjectType('AssistantMessageHistoryItem', {
  description: 'Assistant message history entry',
})
export class AssistantMessageHistoryItemDto {
  @Field((_type) => String)
  uuid: string

  @Field((_type) => String)
  message: string

  @Field((_type) => AssistantMessageTypeEnum)
  type: AssistantMessageTypeEnum

  constructor(value: AssistantMessageHistoryItemDto) {
    this.uuid = value.uuid
    this.message = value.message
    this.type = value.type
  }
}
