/**
 * MCP Client Service
 *
 * This service manages connections to the Model Context Protocol (MCP) server
 * using the Streamable HTTP transport protocol.
 * and give ability to call callTool method
 *
 * Key features:
 * - Uses the standard Streamable HTTP transport
 * - Supports JWT authentication via Authorization headers
 * - Provides connection pooling and retry logic
 * - Offers tools for searching contacts and retrieving contact details
 */
import { createLogger } from '../../../app'

import { createMcpClient } from './mcp-client/create-mcp-client'
import { getServerUrl } from './mcp-client/get-server-url'
import { checkServerHealth } from './check-server-health'
import { parseToolParams } from './parse-tool-params'

import type { ToolNamesValueObject } from '@emma/emma-mcp/types'
import type { Client } from '@modelcontextprotocol/sdk/client/index.js'
import type { McpToolResultValueObject } from '../../core/mcp-tool-result.value-object'

const logger = createLogger('mcp-client.service')

// Log important environment variables for debugging
logger.info('MCP Client Environment:', {
  MCP_SERVER_URL: process.env.MCP_SERVER_URL || 'http://localhost:3030',
})

// Create a singleton instance that can be reused
let globalClientInstance: Client | null = null
let isClientConnecting = false
let connectPromise: Promise<Client> | null = null

export class McpClientService {
  /**
   * Get a connected MCP client instance
   * Uses a global singleton to avoid "already started" errors
   */
  private async getClient(): Promise<Client> {
    // Return existing global client if available
    if (globalClientInstance) {
      try {
        // Check if connection is still valid with a simple test
        await globalClientInstance.listTools()
        logger.info('Using existing connected MCP client')
        return globalClientInstance
      } catch (error) {
        logger.warn(
          'Existing MCP client failed health check, will reconnect',
          error,
        )
        // Fall through to reconnect
        globalClientInstance = null
      }
    }

    // Use a promise to prevent multiple simultaneous connection attempts
    if (isClientConnecting && connectPromise) {
      logger.info(
        'Another connection attempt in progress, waiting for it to complete',
      )
      return connectPromise
    }

    try {
      isClientConnecting = true
      connectPromise = this.createAndConnectClient()
      const client = await connectPromise
      return client
    } finally {
      isClientConnecting = false
      connectPromise = null
    }
  }

  /**
   * Create and connect a new MCP client
   */
  private async createAndConnectClient(): Promise<Client> {
    const mcpUrl = getServerUrl()

    // Check if server is reachable before connecting
    const isHealthy = await checkServerHealth(mcpUrl)
    if (!isHealthy) {
      logger.warn(
        `MCP server health check at ${mcpUrl} failed, but will try to connect anyway`,
      )
      // We'll still try to connect even if the health check fails
    } else {
      logger.info(`MCP server at ${mcpUrl} is available and healthy`)
    }

    // Even if the health check failed, attempt to connect anyway
    // The connection attempt will provide better error details if it fails
    return this.createHttpClient()
  }

  /**
   * Create a client using HTTP transport with retry logic
   */
  private async createHttpClient(): Promise<Client> {
    logger.info('Creating new HTTP MCP client')
    const client = await createMcpClient()
    if (client) {
      globalClientInstance = client
    }
    return client
  }

  /**
   * Reset the global client instance
   */
  private async reset(): Promise<void> {
    logger.info('Resetting MCP client')

    if (globalClientInstance) {
      try {
        await globalClientInstance.close()
      } catch (error) {
        logger.error('Error disconnecting MCP client:', error)
      }
    }

    globalClientInstance = null
    logger.info('MCP client reset complete')
  }

  async callTool({
    name,
    params,
    token,
  }: {
    name: ToolNamesValueObject
    params: Record<string, unknown>
    token: string
  }): Promise<McpToolResultValueObject> {
    const parsedParams = parseToolParams(params)
    logger.info('call-tool', {
      name,
      params: JSON.stringify(parsedParams),
    })

    const client = await this.getClient()
    const headers = {
      Authorization: `Bearer ${token}`,
      'x-lcid': logger.rawLcid,
      'x-session-uuid': logger.context?.sessionUuid
        ? logger.context?.sessionUuid
        : undefined,
    }
    try {
      const res = await client.callTool({
        name,
        arguments: parsedParams,
        headers,
      })

      return res
    } catch (error) {
      logger.error('call-tool-error', {
        name,
        error,
      })

      // Try to recover from connection errors with one retry
      if (
        error instanceof Error &&
        (error.message.includes('Not connected') ||
          error.message.includes('connection') ||
          error.message.includes('network') ||
          error.message.includes('Server not initialized'))
      ) {
        logger.info('Attempting to recover from connection error with retry')
        await this.reset()
        const nextClient = await this.getClient()
        // Reset and get fresh client
        // Retry the tool call
        const nextRes = await nextClient.callTool({
          name,
          arguments: parsedParams,
          headers,
        })

        return nextRes as McpToolResultValueObject
      }

      throw error
    }
  }
}
