import { createLogger } from '../../app'

import { getDailySummaryInterpret } from './tools/get-daily-summary/get-daily-summary.interpret'

import type { ToolNamesValueObject } from '@emma/emma-mcp/types'
import type { McpToolCallValueObject } from '../core/mcp-tool-call.value-object'
import type { McpToolResultValueObject } from '../core/mcp-tool-result.value-object'
import type { AnthropicClient } from './anthropic-client'

const logger = createLogger('anthropic-process-content')

export class ProcessContent {
  constructor(private readonly anthropicClient: AnthropicClient) {}

  async processToolCall(options: {
    toolCall: McpToolCallValueObject
    toolParams?: Record<string, unknown>
    initialContent: string
    toolResult?: McpToolResultValueObject
  }): Promise<{ accumulatedContent: string }> {
    const { toolCall, toolParams, initialContent, toolResult } = options
    // Add processing message
    let updatedContent = initialContent
    const processingMessage =
      '\n\nI am looking up the information you asked for. This will take just a moment...\n\n'
    if (!initialContent.includes(processingMessage)) {
      updatedContent += processingMessage
    }

    // Call appropriate MCP tool based on the request
    if (toolCall.name === 'search_contacts_v2') {
      // For contact search, we need to keep the JSON format to allow the UI to display contact cards
      if (toolResult) {
        if (toolCall.input.query) {
          updatedContent += `Search name:\`\`\`${toolCall.input.query}\`\`\`\n`
        }
        // Format the tool result as content for Claude to continue
        const toolResultContent = JSON.stringify(toolResult, null, 2)
        const contactsLabel = toolParams?.orders
          ? 'Your order based contacts'
          : 'Your contacts'
        updatedContent += toolResultContent.trim()
          ? `${contactsLabel}:\n\`\`\`json\n${toolResultContent}\n\`\`\`\n\n`
          : ''
      }
      return { accumulatedContent: updatedContent }
    }

    if (toolCall.name === 'get_daily_summary') {
      if (toolResult) {
        // For daily summary, send the data to AI for interpretation
        // instead of showing raw JSON to the user
        const interpretedResult = await this.interpretToolResult({
          name: toolCall.name,
          prompt: getDailySummaryInterpret(toolResult),
          data: toolResult,
        })

        // Return the AI's interpretation
        updatedContent += interpretedResult
      }
      return { accumulatedContent: updatedContent }
    }

    // Default handling for any other tools or if no specific handling above was triggered
    if (toolResult) {
      // Format the tool result as content for Claude to continue
      const toolResultContent = JSON.stringify(toolResult, null, 2)
      updatedContent += `Tool result:\n\`\`\`json\n${toolResultContent}\n\`\`\`\n\n`
    } else {
      // Handle case where tool execution didn't return a result
      updatedContent += '\nNo results found.\n'
    }

    return { accumulatedContent: updatedContent }
  }

  private async interpretToolResult({
    name,
    prompt,
    data,
  }: {
    name: ToolNamesValueObject
    prompt?: string
    data: McpToolResultValueObject
  }): Promise<string> {
    try {
      const usedPrompt =
        prompt ??
        `Please analyze and interpret this data in a user-friendly way:
${JSON.stringify(data, null, 2)}`
      logger.log(`[interpretToolResult] Interpreting ${name} data`)

      logger.log('[interpretToolResult] start interpret data')

      // Send to Claude for interpretation
      const response =
        await this.anthropicClient.interpretationRequest(usedPrompt)

      if (response?.content) {
        logger.log('[interpretToolResult] Successfully interpreted data')
        return response.content
      }
      logger.warn('[interpretToolResult] Empty response from AI interpretation')
      return `I analyzed your information but couldn't generate a meaningful summary. Here's what I found:\n\n${JSON.stringify(
        data,
        null,
        2,
      )}`
    } catch (error) {
      logger.error('Error interpreting tool result:', error)
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'

      // Fallback to raw data with error message
      return `I tried to analyze your information but encountered an error: ${errorMessage}. Here's the raw data:\n\n${JSON.stringify(data, null, 2)}`
    }
  }
}
