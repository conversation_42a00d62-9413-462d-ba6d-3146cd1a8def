import { afterEach, beforeEach, describe, expect, it } from '@repo/unit-test'
import { vi } from 'vitest'

import * as fixJsonModule from './attempt-to-fix-incomplete-json'
import { parseStream } from './parse-stream'

describe('parseStream', () => {
  // Common test setup
  let parsedInputs: Record<number, Record<string, unknown>>
  let activeToolInputs: Map<number, string>
  let completedBlockIndices: Set<number>
  let contentBlocks: any[]

  beforeEach(() => {
    // Reset test data before each test
    parsedInputs = {}
    activeToolInputs = new Map()
    completedBlockIndices = new Set()
    contentBlocks = []

    // Reset console methods
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should handle content_block_start events for tool_use', () => {
    const chunk = {
      type: 'content_block_start',
      content_block: {
        type: 'tool_use',
        name: 'testTool',
      },
    } as any

    parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(contentBlocks.length).toBe(1)
    expect(contentBlocks[0].name).toBe('testTool')
    expect(contentBlocks[0].partialInputJson).toBe('')
  })

  it('should handle text_delta events and return message delta', () => {
    const chunk = {
      type: 'content_block_delta',
      delta: {
        type: 'text_delta',
        text: 'Hello world',
      },
    } as any

    const result = parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(result).toEqual({
      type: 'msg',
      delta: 'Hello world',
    })
  })

  it('should accumulate JSON for input_json_delta events', () => {
    // Setup a content block first
    contentBlocks.push({
      type: 'tool_use',
      name: 'testTool',
      partialInputJson: '',
    })

    const chunk = {
      type: 'content_block_delta',
      index: 0,
      delta: {
        type: 'input_json_delta',
        partial_json: '{"query": "test',
      },
    } as any

    parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(activeToolInputs.get(0)).toBe('{"query": "test')
    expect(contentBlocks[0].partialInputJson).toBe('{"query": "test')

    // Send another chunk to test accumulation
    const chunk2 = {
      type: 'content_block_delta',
      index: 0,
      delta: {
        type: 'input_json_delta',
        partial_json: '"}',
      },
    } as any

    parseStream({
      parsedInputs,
      chunk: chunk2,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(activeToolInputs.get(0)).toBe('{"query": "test"}')
    expect(contentBlocks[0].partialInputJson).toBe('{"query": "test"}')
  })

  it('should parse JSON when content_block_stop is received with valid JSON', () => {
    // Setup a content block and accumulated JSON
    contentBlocks.push({
      type: 'tool_use',
      name: 'testTool',
      partialInputJson: '{"query": "test"}',
    })
    activeToolInputs.set(0, '{"query": "test"}')

    const chunk = {
      type: 'content_block_stop',
      index: 0,
    } as any

    parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(completedBlockIndices.has(0)).toBe(true)
    expect(parsedInputs[0]).toEqual({ query: 'test' })
    expect(contentBlocks[0].input).toEqual({ query: 'test' })
  })

  it('should attempt to fix incomplete JSON when content_block_stop is received', () => {
    // Setup a content block with incomplete JSON
    contentBlocks.push({
      type: 'tool_use',
      name: 'testTool',
      partialInputJson: '{"query": "test',
    })
    activeToolInputs.set(0, '{"query": "test')

    // Add spy for attemptToFixIncompleteJson
    vi.spyOn(fixJsonModule, 'attemptToFixIncompleteJson').mockReturnValue({
      query: 'mocked-query',
    })

    const chunk = {
      type: 'content_block_stop',
      index: 0,
    } as any

    parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(fixJsonModule.attemptToFixIncompleteJson).toHaveBeenCalledWith(
      '{"query": "test',
    )
    expect(parsedInputs[0]).toEqual({ query: 'mocked-query' })
    expect(contentBlocks[0].input).toEqual({ query: 'mocked-query' })
  })

  it('should return tool call when message_delta with stop_reason=tool_use is received', () => {
    // Setup completed blocks and parsed inputs
    contentBlocks.push({
      type: 'tool_use',
      name: 'testTool',
      partialInputJson: '{"query": "test"}',
      input: { query: 'test' },
    })
    parsedInputs[0] = { query: 'test' }
    completedBlockIndices.add(0)

    const chunk = {
      type: 'message_delta',
      delta: {
        stop_reason: 'tool_use',
      },
    } as any

    const result = parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(result).toEqual({
      type: 'tool',
      tool: {
        name: 'testTool',
        input: { query: 'test' },
      },
    })
  })

  it('should handle message_stop events', () => {
    const chunk = {
      type: 'message_stop',
    } as any

    const result = parseStream({
      parsedInputs,
      chunk,
      activeToolInputs,
      completedBlockIndices,
      contentBlocks,
    })

    expect(result).toBeUndefined()
  })
})
