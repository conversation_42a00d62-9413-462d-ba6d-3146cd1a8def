import { attemptToFixIncompleteJson } from './attempt-to-fix-incomplete-json'

import type { RawMessageStreamEvent } from '@anthropic-ai/sdk/resources/messages/messages'
import type { McpToolCallValueObject } from '../../core/mcp-tool-call.value-object'
import type { StreamToolUseBlockValueObject } from '../../core/stream-tool-use-block-value.object'

type Args = {
  parsedInputs: Record<number, Record<string, unknown>>
  activeToolInputs: Map<number, string>
  chunk: RawMessageStreamEvent
  completedBlockIndices: Set<number>
  contentBlocks: StreamToolUseBlockValueObject[]
}

type StreamCollectResult =
  | {
      type: 'msg'
      delta: string
    }
  | {
      type: 'tool'
      tool: McpToolCallValueObject
    }

export function parseStream({
  parsedInputs,
  chunk,
  activeToolInputs,
  completedBlockIndices,
  contentBlocks,
}: Args): StreamCollectResult | undefined {
  if (
    chunk.type === 'content_block_start' &&
    chunk.content_block?.type === 'tool_use'
  ) {
    // Cast is needed due to SDK type limitations
    const toolUseBlock =
      chunk.content_block as unknown as StreamToolUseBlockValueObject
    toolUseBlock.partialInputJson = '' // Initialize the partial JSON tracker
    contentBlocks.push(toolUseBlock)

    console.debug(
      '[Anthropic] Tool use block started:',
      JSON.stringify(toolUseBlock),
      'index:',
      contentBlocks.length - 1,
    )
  }

  // Handle different types of stream events
  // console.debug('[Anthropic] Chunk:', chunk.type, chunk)

  // Handle content blocks
  if (chunk.type === 'content_block_delta') {
    if (chunk.delta.type === 'text_delta' && 'text' in chunk.delta) {
      return {
        type: 'msg',
        delta: chunk.delta.text,
      }
    } else if (
      chunk.delta.type === 'input_json_delta' &&
      'partial_json' in chunk.delta
    ) {
      // Track JSON input accumulation for tool calls using the global map
      if (chunk.index !== undefined) {
        console.log(
          '[Anthropic] Tool input JSON delta for index',
          chunk.index,
          ':',
          chunk.delta.partial_json,
        )

        // Initialize if not exists
        if (!activeToolInputs.has(chunk.index)) {
          activeToolInputs.set(chunk.index, '')
        }

        // Accumulate delta
        const currentJson = activeToolInputs.get(chunk.index) || ''
        const updatedJson = currentJson + chunk.delta.partial_json
        activeToolInputs.set(chunk.index, updatedJson)

        console.log(
          '[Anthropic] Accumulated JSON for index',
          chunk.index,
          ':',
          updatedJson,
        )

        // Also update the content block if it exists
        if (contentBlocks.length > chunk.index) {
          const block = contentBlocks[chunk.index]
          block.partialInputJson = updatedJson
          console.log(
            '[Anthropic] Updated content block',
            chunk.index,
            'with accumulated JSON',
          )
        }
      }
    }
  }
  // Handle content_block_stop events to parse accumulated JSON
  else if (chunk.type === 'content_block_stop') {
    console.debug('[Anthropic] Content block stopped at index:', chunk.index)

    // Mark this block as completed
    if (chunk.index !== undefined) {
      completedBlockIndices.add(chunk.index)

      // Get the accumulated JSON from our map
      const accumulatedJson = activeToolInputs.get(chunk.index)
      console.log(
        '[Anthropic] Final accumulated JSON for stopped block',
        chunk.index,
        ':',
        accumulatedJson,
      )

      // Get the corresponding content block
      if (contentBlocks.length > chunk.index) {
        const block = contentBlocks[chunk.index]

        // Ensure the block has the latest accumulated JSON
        if (accumulatedJson) {
          block.partialInputJson = accumulatedJson
        }

        // Only process tool_use blocks
        if (block && block.type === 'tool_use' && block.partialInputJson) {
          try {
            // Parse the accumulated JSON now that the block is complete
            const cleanJson = block.partialInputJson.trim()
            console.log(
              `[Anthropic] Final accumulated JSON for block ${chunk.index}:`,
              cleanJson,
            )

            // Only parse if it looks like valid JSON
            if (cleanJson.startsWith('{') && cleanJson.endsWith('}')) {
              parsedInputs[chunk.index] = JSON.parse(cleanJson)
              console.log(
                `[Anthropic] Parsed JSON for block ${chunk.index}:`,
                parsedInputs[chunk.index],
              )

              // Update the input property of the content block with the parsed JSON
              block.input = parsedInputs[chunk.index]
            } else {
              // Try to fix incomplete JSON
              const fixedJson = attemptToFixIncompleteJson(cleanJson)
              if (fixedJson) {
                parsedInputs[chunk.index] = fixedJson
                console.log(
                  `[Anthropic] Fixed and parsed JSON for block ${chunk.index}:`,
                  parsedInputs[chunk.index],
                )

                // Update the input property of the content block with the fixed JSON
                block.input = parsedInputs[chunk.index]
              }
            }
          } catch (error) {
            console.warn(
              `[Anthropic] Failed to parse JSON for block ${chunk.index}:`,
              error,
              'Raw JSON:',
              block.partialInputJson,
            )
          }
        }
      }
    }
  }
  // Handle message delta stop_reason (should call tool here)
  else if (
    chunk.type === 'message_delta' &&
    chunk.delta.stop_reason === 'tool_use'
  ) {
    // This indicates the model is requesting to use a tool
    console.debug(
      '[Anthropic] Tool use requested in message delta',
      chunk.delta,
    )

    // Debug: Log all accumulated JSON from our global map
    console.log(
      '[Anthropic] All accumulated JSON before tool execution:',
      Array.from(activeToolInputs.entries()).map(([index, json]) => ({
        index,
        json,
      })),
    )

    // Debug: Log the state of all content blocks before processing
    console.log(
      '[Anthropic] All content blocks before tool execution:',
      contentBlocks.map((b) => ({
        index: contentBlocks.indexOf(b),
        type: b.type,
        name: b.name,
        partialInputJson: b.partialInputJson,
        input: b.input,
      })),
    )

    try {
      // Use the most recent tool_use block
      if (contentBlocks.length > 0) {
        const lastToolBlock = contentBlocks[contentBlocks.length - 1]
        console.log(
          '[Anthropic] Last tool block:',
          JSON.stringify(lastToolBlock),
        )

        // First try to use the parsed input from the content_block_stop event
        let toolName = lastToolBlock.name
        let toolArgs: Record<string, unknown> = {}

        // Priority 1: Find the last completed block with parsed input
        const lastCompletedIndices = Array.from(completedBlockIndices)
        if (lastCompletedIndices.length > 0) {
          const lastCompletedIndex = Math.max(...lastCompletedIndices)
          if (lastCompletedIndex >= 0 && parsedInputs[lastCompletedIndex]) {
            toolArgs = parsedInputs[lastCompletedIndex]
            toolName = contentBlocks[lastCompletedIndex].name
            console.log(
              `[Anthropic] Using parsed input from block ${lastCompletedIndex}:`,
              toolArgs,
            )
          } else if (
            contentBlocks[lastCompletedIndex]?.input &&
            Object.keys(contentBlocks[lastCompletedIndex].input).length > 0
          ) {
            // If we have input directly on the content block, use that
            toolArgs = contentBlocks[lastCompletedIndex].input
            toolName = contentBlocks[lastCompletedIndex].name
            console.log(
              `[Anthropic] Using input directly from block ${lastCompletedIndex}:`,
              toolArgs,
            )
          }
        }

        // Priority 2: Use the accumulated JSON from our active tool inputs map
        if (Object.keys(toolArgs).length === 0) {
          // Find the last block with accumulated JSON
          let lastInputIndex = -1
          let fullestInput = ''

          for (const [indexStr, json] of activeToolInputs.entries()) {
            const index = Number(indexStr)
            // Find the most complete accumulated JSON
            if (json && json.length > fullestInput.length) {
              fullestInput = json
              lastInputIndex = index
            }
          }

          if (lastInputIndex >= 0 && fullestInput) {
            console.log(
              `[Anthropic] Found accumulated JSON from activeToolInputs for index ${
                lastInputIndex
              }:`,
              fullestInput,
            )

            try {
              // Try to parse the accumulated JSON
              const cleanInput = fullestInput.trim()
              if (cleanInput.startsWith('{') && cleanInput.endsWith('}')) {
                toolArgs = JSON.parse(cleanInput)
                console.log(
                  '[Anthropic] Parsed accumulated JSON for tool call:',
                  toolArgs,
                )

                // If we have a valid index in contentBlocks, update toolName
                if (contentBlocks.length > lastInputIndex) {
                  toolName = contentBlocks[lastInputIndex].name
                }
              } else {
                // Try to extract specific fields if JSON is incomplete
                const extractedArgs = attemptToFixIncompleteJson(cleanInput)
                if (extractedArgs) {
                  toolArgs = extractedArgs
                  console.log(
                    '[Anthropic] Extracted parameters from accumulated JSON:',
                    toolArgs,
                  )
                }
              }
            } catch (e) {
              console.log('[Anthropic] Error parsing accumulated JSON:', e)
            }
          }
        }

        // Priority 3: Fallback to previous methods if neither of the above worked
        if (Object.keys(toolArgs).length === 0) {
          // Find the block with the most complete partial JSON
          let mostCompleteJsonBlock = lastToolBlock
          let mostCompleteJson = lastToolBlock.partialInputJson || ''

          for (const block of contentBlocks) {
            if (
              block.partialInputJson &&
              block.partialInputJson.length > mostCompleteJson.length
            ) {
              mostCompleteJson = block.partialInputJson
              mostCompleteJsonBlock = block
            }
          }

          console.log('[Anthropic] Most complete JSON found:', mostCompleteJson)

          // Access tool information
          toolName = mostCompleteJsonBlock.name

          // Parse the JSON from the partial input
          if (mostCompleteJson) {
            try {
              // Per Anthropic docs, input_json_delta gives partial JSON strings
              // that should be accumulated and then parsed when complete
              const cleanJson = mostCompleteJson.trim()

              // Check if the JSON is complete (has opening and closing braces)
              const isCompleteJson =
                cleanJson.startsWith('{') && cleanJson.endsWith('}')

              if (isCompleteJson) {
                try {
                  // Parse the complete JSON
                  toolArgs = JSON.parse(cleanJson)
                  console.log(
                    '[Anthropic] Parsed complete JSON object:',
                    toolArgs,
                  )
                } catch (e) {
                  console.log(
                    '[Anthropic] JSON parsing failed, will try to extract fields:',
                    e,
                  )

                  // Extract query parameter if it exists
                  const queryMatch = cleanJson.match(/"query"\s*:\s*"([^"]+)"/)
                  if (queryMatch?.[1]) {
                    toolArgs.query = queryMatch[1]
                    console.log(
                      '[Anthropic] Extracted query parameter:',
                      toolArgs.query,
                    )
                  }
                }
              } else {
                // Handle incomplete JSON by looking for specific patterns
                const queryMatch = cleanJson.match(/"query"\s*:\s*"([^"]+)"/)
                if (queryMatch?.[1]) {
                  toolArgs.query = queryMatch[1]
                  console.log(
                    '[Anthropic] Extracted query parameter from incomplete JSON:',
                    toolArgs.query,
                  )
                }
              }
            } catch (error) {
              console.warn(
                'Error processing tool input JSON:',
                error,
                'Raw JSON:',
                mostCompleteJson,
              )
            }
          } else if (
            mostCompleteJsonBlock.input &&
            Object.keys(mostCompleteJsonBlock.input).length > 0
          ) {
            // If partialInputJson is empty but the input field has content, use that
            toolArgs = mostCompleteJsonBlock.input
            console.log(
              '[Anthropic] Using predefined input from tool block:',
              toolArgs,
            )
          }
        }

        const toolCall: McpToolCallValueObject = {
          name: toolName,
          input: toolArgs,
        }

        console.log(
          'Tool call request:',
          toolCall.name,
          JSON.stringify(toolCall.input),
        )

        return {
          type: 'tool',
          tool: toolCall,
        }
      }
    } catch (error) {
      console.error('Error handling tool call:', error)
      const errorDelta = `\n\nI'm sorry I can't access your contact list. Can you try again later or check your connection.\n\n`

      return {
        type: 'msg',
        delta: errorDelta,
      }
    }
  }
  // Handle message_stop events
  else if (chunk.type === 'message_stop') {
    console.debug('[Anthropic] Message stopped')
  }
}
