import { expressMiddleware } from '@apollo/server/express4'

import { createContext } from '../../modules/server/interface/create-context'

import type { ApolloServer } from '@apollo/server'
import type { JobManager } from '@emma/jobs'
import type { Request } from 'express'
import type { PubSubInstance } from '../../modules/pubsub/core/pubsub.instance'

export function gqlContextMiddleware({
  pubSub,
  gqlServer,
  jobManager,
}: {
  pubSub: PubSubInstance
  jobManager: JobManager
  gqlServer: ApolloServer
}) {
  const gqlMiddleware = expressMiddleware(gqlServer, {
    context: async ({ req }: { req: Request }) => {
      const context = await createContext({
        pubSub,
        headers: req.headers,
        url: req.url,
        method: req.method,
        jobManager,
      })

      return context
    },
  })

  return gqlMiddleware
}
