import { z, type ZodSchema } from 'zod'

import { alphaNum } from './custom-validations/alpha-num'
import { customHandler } from './custom-validations/custom-handler'
import { emailMax } from './custom-validations/email-max'
import { fieldName } from './custom-validations/field-name'
import { firstName } from './custom-validations/first-name'
import { hasBooleanTrue } from './custom-validations/has-boolean-true'
import { lastName } from './custom-validations/last-name'
import { nonEmptyString } from './custom-validations/non-empty-string'
import { oneOf } from './custom-validations/one-of'
import { otpCode } from './custom-validations/otp-code'
import { paginationTake } from './custom-validations/pagination-take'
import { password } from './custom-validations/password'
import { sort } from './custom-validations/sort'
import { time } from './custom-validations/time'
import { validateItemList } from './custom-validations/validate-item-list'
import { getInitialFields } from './get-initial-fields'
import { handleValidate } from './handle-validate'

import type { FailedValidation, SuccessValidation } from './handle-validate'

export type ValidatorInitialFields<T extends z.ZodRawShape> = Partial<
  z.infer<z.ZodObject<T>>
>

export type ValidatorOptions<T extends z.ZodRawShape> = {
  name?: string
  onFieldChange?: (name: any, value: any) => void
  onStateChange?: () => void
  initialFields?: ValidatorInitialFields<T>
}

export type ValidatorSchemeObject<T extends z.ZodRawShape> = z.ZodObject<T>
export type ValidatorSchemeType = z.ZodRawShape
export type ValidatorSchema = ZodSchema
export type ValidatorIncomingType<T extends z.ZodRawShape> = z.infer<
  z.ZodObject<T>
>

export type ValidatorInfer<T extends z.ZodType<any, any, any>> = z.infer<T>

export class Validator<T extends z.ZodRawShape> {
  private readonly zodScheme: z.ZodObject<T>
  private readonly initialFields: z.infer<z.ZodObject<T>>
  private readonly initialErrorFields: {
    [k in keyof z.baseObjectOutputType<T>]: string
  }
  private readonly onFieldChange?: (name: any, value: any) => void
  private readonly onStateChange?: () => void
  public values: z.infer<z.ZodObject<T>>
  public errors: { [k in keyof z.baseObjectOutputType<T>]: string }
  public handlers: {
    [k in NonNullable<keyof z.baseObjectOutputType<T>>]: (
      args?: z.baseObjectOutputType<T>[k],
    ) => void
  }
  static scheme = {
    ...z,
    sort,
    time,
    hasBooleanTrue,
    validateItemList,
    customHandler,
    emailMax,
    firstName,
    lastName,
    password,
    nonEmptyString,
    fieldName,
    otpCode,
    paginationTake,
    alphaNum,
    oneOf,
  }

  constructor(scheme: T, options?: ValidatorOptions<T>) {
    this.onFieldChange = options?.onFieldChange
    this.onStateChange = options?.onStateChange

    this.zodScheme = z.object(scheme)
    const initialFields = getInitialFields(this.zodScheme)
    this.initialFields = { ...initialFields, ...options?.initialFields }

    const keys = Object.keys(scheme) as Array<keyof z.baseObjectOutputType<T>>

    this.initialErrorFields = keys.reduce(
      (acc, key) => {
        acc[key] = ''
        return acc
      },
      {} as {
        [k in keyof T]: string
      },
    )

    this.errors = { ...this.initialErrorFields }
    this.values = { ...this.initialFields }

    this.handlers = keys.reduce(
      (acc, fieldName) => {
        acc[fieldName] = (
          value?: z.baseObjectOutputType<T>[typeof fieldName],
        ) => {
          this.handleFieldChange(fieldName, value)
        }
        return acc
      },
      {} as {
        [k in keyof z.baseObjectOutputType<T>]: (
          args?: z.baseObjectOutputType<T>[k],
        ) => void
      },
    )
  }

  reset(fieldNames?: (keyof Partial<z.infer<z.ZodObject<T>>>)[]): void {
    if (fieldNames) {
      fieldNames.forEach((fieldName) => {
        this.values[fieldName] = this.initialFields[fieldName]
        this.errors[fieldName] = this.initialErrorFields[fieldName]
      })
    }
    if (!fieldNames) {
      this.errors = { ...this.initialErrorFields }
      this.values = { ...this.initialFields }
    }
    this.onStateChange && this.onStateChange()
  }

  setError<V extends keyof z.baseObjectOutputType<T>>(
    fieldName: V,
    message: string,
  ): void {
    if (this.errors[fieldName] === message) {
      return
    }

    this.errors[fieldName] = message
    this.onStateChange && this.onStateChange()
  }

  submit(): SuccessValidation<T> | FailedValidation<T> {
    this.errors = {
      ...this.initialErrorFields,
    }
    const result = handleValidate(this.zodScheme, this.values)
    if (!result.isValid) {
      this.errors = result.errors
    }

    this.onStateChange && this.onStateChange()
    return result
  }

  setValues(values: Partial<z.infer<z.ZodObject<T>>>): void {
    this.errors = { ...this.initialErrorFields }
    this.values = {
      ...this.initialFields,
      ...values,
    }
    this.onStateChange && this.onStateChange()
  }

  private handleFieldChange<V extends keyof z.baseObjectOutputType<T>>(
    fieldName: V,
    fieldValue: z.baseObjectOutputType<T>[V] | undefined,
  ): void {
    this.setError(fieldName, this.initialErrorFields[fieldName])

    // eslint-disable-next-line eslint-comments/no-restricted-disable
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    this.values[fieldName] = fieldValue
    this.onFieldChange?.(fieldName, fieldValue)
    this.onStateChange && this.onStateChange()
  }
}
